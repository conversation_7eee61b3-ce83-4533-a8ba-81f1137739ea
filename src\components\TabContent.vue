<template>
  <div class="tab-content" :class="{ 'fullscreen-mode': isFullscreenMode }">
    <!-- 根据标签页类型显示不同的内容 -->
    <component
      :is="contentComponent"
      v-if="contentComponent"
      :email="tab.email"
      :tags="tags"
      :initial-data="tab.composeData"
      :customer-data="tab.customerData"
      :contact-data="tab.contactData"
      @close="closeTab"
      @save-success="handleSaveSuccess"
      @cancel="closeTab"
      @send="handleEmailSend"
    />

    <!-- 如果没有匹配的组件，显示默认内容 -->
    <div v-else class="default-content">
      <h3>{{ tab.title }}</h3>
      <p>内容正在加载中...</p>
    </div>
  </div>
</template>

<script>
import NewClueForm from '@/views/crm/email/components/NewClueForm.vue'
import FullscreenEmailView from '@/views/crm/email/components/FullscreenEmailView.vue'
import EmailComposer from '@/views/crm/email/components/EmailComposer.vue'

export default {
  name: 'TabContent',
  components: {
    NewClueForm,
    FullscreenEmailView,
    EmailComposer
  },
  props: {
    tab: {
      type: Object,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    contentComponent() {
      // 根据标签页类型返回对应的组件
      const typeComponentMap = {
        'clue': 'NewClueForm',
        'fullscreen': 'FullscreenEmailView',
        'order': 'NewClueForm', // 暂时使用NewClueForm，后续可以创建专门的订单组件
        'compose': 'EmailComposer', // 写邮件组件
        'calendar': 'CalendarView', // 日历组件（待实现）
        'contacts': 'ContactsView', // 联系人组件（待实现）
        'customer': 'CustomerView' // 客户详情组件（待实现）
      };

      const component = typeComponentMap[this.tab.type] || null;
      console.log('🔥 TabContent contentComponent', {
        tabType: this.tab.type,
        component: component,
        tab: this.tab
      });

      return component;
    },

    // 是否为全屏模式
    isFullscreenMode() {
      return this.tab.isFullscreen === true;
    }
  },
  methods: {
    closeTab() {
      // 发出关闭事件，传递tabId和index（index在父组件中计算）
      this.$emit('close', { tabId: this.tab.id });
    },

    handleSaveSuccess(data) {
      this.$emit('save-success', {
        tabId: this.tab.id,
        data: data
      });

      // 保存成功后关闭标签页
      this.closeTab();
    },

    handleEmailSend(emailData) {
      // 处理邮件发送
      this.$emit('email-send', {
        tabId: this.tab.id,
        emailData: emailData
      });

      // 发送成功后关闭标签页
      this.closeTab();
    }
  }
}
</script>

<style scoped>
.tab-content {
  height: 100%;
  overflow: auto;
  background-color: #fff;
  position: relative;
  z-index: 200 !important; /* 设置为比tab-navigation低但比其他元素高的z-index */

  &.fullscreen-mode {
    /* 全屏模式样式 */
    display: flex;
    flex-direction: column;
    padding-top: 40px; /* 添加内边距，为tab导航留出空间 */
  }
}

.default-content {
  padding: 24px;
  text-align: center;
  color: #606266;
}
</style>
